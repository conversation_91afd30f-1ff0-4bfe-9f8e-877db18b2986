"use client";

import { usePathname, useSearchParams } from "next/navigation";
import posthog from "posthog-js";
import { PostHogProvider as PHProvider, usePostHog } from "posthog-js/react";
import { Suspense, useEffect } from "react";
import { env } from "@/env";
import { useSession } from "@/lib/auth-client";
import { posthogCookieUtils } from "@/lib/cookie-utils";

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    posthog.init(env.NEXT_PUBLIC_POSTHOG_KEY, {
      api_host: "/api/analytics",
      ui_host: "https://us.posthog.com",
      capture_pageview: false, // We capture pageviews manually
      capture_pageleave: true, // Enable pageleave capture
    });
  }, []);

  return (
    <PHProvider client={posthog}>
      <SuspendedPostHogPageView />
      {children}
    </PHProvider>
  );
}

function PostHogPageView() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const posthog = usePostHog();
  const { data: session } = useSession();

  // Initialize anonymous tracking for waitlist users
  useEffect(() => {
    if (posthog && typeof window !== "undefined") {
      // Ensure we have a distinct ID for anonymous users
      const anonymousId = posthogCookieUtils.getOrCreateAnonymousId(
        env.NEXT_PUBLIC_POSTHOG_KEY,
      );

      // If no session and not identified, ensure we have anonymous tracking
      if (!session && !posthog._isIdentified()) {
        // Check if PostHog already has a distinct_id
        const currentDistinctId = posthogCookieUtils.getDistinctId(
          env.NEXT_PUBLIC_POSTHOG_KEY,
        );

        if (!currentDistinctId) {
          // Force PostHog to use our anonymous ID by calling identify
          // This ensures consistent tracking across page loads
          posthog.identify(anonymousId, {
            $is_anonymous: true,
            $anonymous_id: anonymousId,
          });
        }
      }
    }
  }, [posthog, session]);

  useEffect(() => {
    if (pathname && posthog && typeof window !== "undefined") {
      let url = window.origin + pathname;
      const search = searchParams.toString();
      if (search) {
        url += `?${search}`;
      }
      posthog.capture("$pageview", { $current_url: url });
    }
  }, [pathname, searchParams, posthog]);

  useEffect(() => {
    if (session?.user && !posthog._isIdentified()) {
      // Get the anonymous ID before identifying
      const anonymousId = posthogCookieUtils.getDistinctId(
        env.NEXT_PUBLIC_POSTHOG_KEY,
      );

      // Identify the user
      posthog.identify(session.user.id, {
        email: session.user.email,
        username: session.user.name,
        user_type: session.user.role,
      });

      // If we had an anonymous ID, alias it to the identified user
      if (anonymousId && anonymousId !== session.user.id) {
        posthog.alias(session.user.id, anonymousId);
      }
    }

    if (!session && posthog._isIdentified()) {
      posthog.reset();

      // Re-establish anonymous tracking after reset
      const anonymousId = posthogCookieUtils.getOrCreateAnonymousId(
        env.NEXT_PUBLIC_POSTHOG_KEY,
      );

      // Use identify to set the anonymous ID after reset
      posthog.identify(anonymousId, {
        $is_anonymous: true,
        $anonymous_id: anonymousId,
      });
    }
  }, [posthog, session]);

  return null;
}

function SuspendedPostHogPageView() {
  return (
    <Suspense fallback={null}>
      <PostHogPageView />
    </Suspense>
  );
}
