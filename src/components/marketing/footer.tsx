import Link from "next/link";
import { FaFace<PERSON>, FaInstagram, FaXTwitter } from "react-icons/fa6";

const socialNavigation = [
  {
    name: "Facebook",
    href: "#",
    icon: (props: React.ComponentProps<typeof FaFacebook>) => (
      <FaFacebook {...props} />
    ),
  },
  {
    name: "Instagram",
    href: "#",
    icon: (props: React.ComponentProps<typeof FaInstagram>) => (
      <FaInstagram {...props} />
    ),
  },
  {
    name: "X",
    href: "#",
    icon: (props: React.ComponentProps<typeof FaXTwitter>) => (
      <FaXTwitter {...props} />
    ),
  },
];

const legalNavigation = [
  { name: "Privacy Policy", href: "/privacy" },
  { name: "Terms of Service", href: "/terms" },
];

export default function Footer() {
  return (
    <footer className="bg-white">
      <div className="relative mx-auto max-w-7xl px-6 py-16 lg:px-8">
        {/* Main footer content */}
        <div className="pt-16">
          <div className="md:flex md:items-center md:justify-between">
            {/* Social links */}
            <div className="flex justify-center gap-x-6 md:order-2">
              {socialNavigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-gray-500 transition-colors hover:text-orange-600"
                >
                  <span className="sr-only">{item.name}</span>
                  <item.icon aria-hidden="true" className="size-6" />
                </a>
              ))}
            </div>

            {/* Copyright */}
            <p className="mt-8 text-center text-gray-600 text-sm/6 md:order-1 md:mt-0">
              &copy; 2025 TradeCrews Inc. All rights reserved.
            </p>
          </div>

          {/* Legal links */}
          <div className="mt-12 border-gray-200 border-t pt-8">
            <div className="flex flex-col items-center justify-center gap-6 sm:flex-row sm:gap-8">
              {legalNavigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="font-medium text-gray-600 text-sm transition-colors hover:text-orange-600"
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
